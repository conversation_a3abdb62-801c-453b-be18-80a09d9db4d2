<script lang="ts">
	import UpdateStarsIcon from "$lib/components/icons/ui/UpdateStarsIcon.svelte";
	import { But<PERSON> } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import * as Accordion from "$lib/components/ui/accordion";
	import { faqs } from "$config";
</script>

<div class="page-spacing">
	<section class="section-spacing">
		<!-- Hero Section -->
		<div class="lg:px-18 flex w-full flex-col justify-center gap-4 py-6 md:px-8">
			<div class="flex flex-col gap-4">
				<a
					class="md:text-smlg subsection-subitem-text flex h-fit w-fit items-center justify-start gap-1 rounded-lg border px-2 py-1 md:gap-2"
					href="/changelog"
				>
					<UpdateStarsIcon class="md:size-5.5 size-5" />
					<h6 class="tracking-tight">Version 1.0 is live</h6>
				</a>
				<div class="mx-auto w-full">
					<div class="flex flex-col justify-between gap-8 md:flex-row md:items-center">
						<h1 class="headline-text md:max-w-full">
							Smart skill tracking for future-ready institutions.
						</h1>
						<div class="flex flex-col gap-5 md:max-w-[35%]">
							<p class="page-text">
								Evoprof is your trusted partner for managing student skill profiles, institutional
								reporting, and secure data sharing — powered by AI and blockchain.
							</p>
							<div class="flex gap-4">
								<Button href="/contact-sales">Contact Sales</Button>
								<Button variant="outline" href="/pricing">Explore Plans</Button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Section -->
	<section class="section-spacing flex flex-col gap-8">
		<SectionHeader
			sectionName="FAQ"
			headline="Here are the answers to your questions"
			subheadline="<div class='flex flex-col gap-1'><p>After reading this section, if you still have questions,</p><p>feel free to <a href='/contact' class='text-primary decoration-1 underline'>reach out</a> to us.</p></div>"
			color="#00AD09"
		/>

		<Accordion.Root type="single" class="min-w-full md:w-[600px]">
			{#each faqs as faq (faq.question)}
				<Accordion.Item value={faq.question}>
					<Accordion.Trigger>{faq.question}</Accordion.Trigger>
					<Accordion.Content>
						<p class="">{faq.answer}</p>
					</Accordion.Content>
				</Accordion.Item>
			{/each}
		</Accordion.Root>
	</section>
</div>
