<script lang="ts" module>
	import { tv, type VariantProps } from "tailwind-variants";

	export const accordionTriggerVariants = tv({
		base: "flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all hover:underline focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",
		variants: {
			variant: {
				default: "px-4",
				ghost: "px-0",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	});

	export type AccordionTriggerVariant = VariantProps<typeof accordionTriggerVariants>["variant"];
</script>

<script lang="ts">
	import { cn, type WithElementRef } from "$lib/utils.js";
	import type { HTMLButtonAttributes } from "svelte/elements";
	import { getContext } from "svelte";

	let {
		ref = $bindable(null),
		class: className,
		variant = "default",
		children,
		...restProps
	}: WithElementRef<HTMLButtonAttributes> & {
		variant?: AccordionTriggerVariant;
	} = $props();

	const itemContext = getContext<{
		value: string;
		disabled: boolean;
		isOpen: boolean;
		toggle: () => void;
	}>("accordion-item");

	if (!itemContext) {
		throw new Error("AccordionTrigger must be used within an AccordionItem component");
	}

	function handleClick() {
		itemContext.toggle();
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === "Enter" || event.key === " ") {
			event.preventDefault();
			itemContext.toggle();
		}
	}
</script>

<button
	bind:this={ref}
	type="button"
	data-slot="accordion-trigger"
	data-state={itemContext.isOpen ? "open" : "closed"}
	id="accordion-trigger-{itemContext.value}"
	aria-expanded={itemContext.isOpen}
	aria-controls="accordion-content-{itemContext.value}"
	disabled={itemContext.disabled}
	class={cn(accordionTriggerVariants({ variant }), className)}
	onclick={handleClick}
	onkeydown={handleKeydown}
	{...restProps}
>
	{@render children?.()}
	<svg
		class="h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200"
		fill="none"
		stroke="currentColor"
		viewBox="0 0 24 24"
		xmlns="http://www.w3.org/2000/svg"
		aria-hidden="true"
	>
		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
	</svg>
</button>
